import json
import logging
import os
import re

import httpx
from mitmproxy import http

logging.basicConfig(level=logging.WARNING)
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

cfg_session_id = os.environ.get("SESSION_ID")
cfg_token = os.environ.get("TOKEN")
cfg_node_id = os.environ.get("NODE_ID")
cfg_augment_gateway_base_url = os.environ.get("AUGMENT_GATEWAY_BASE_URL")
cfg_default_email = os.environ.get("DEFAULT_EMAIL", "<EMAIL>")
cfg_suspended_message = """
Tài khoản của bạn không còn sử dụng được nữa. Truy cập https://augmentgateway.1app.space để đổi sang tài khoản mới.

*Lưu ý sau khi đổi tài khoản:
- CẦN BẮT ĐẦU 1 CUỘC TRÒ CHUYỆN MỚI NẾU KHÔNG TÀI KHOẢN MỚI SẼ BỊ KHÓA.
- Hãy yên tâm các thiết lập và codebase của bạn sẽ không cần index lại sau khi đổi tài khoản.*

Shop cảm ơn bạn đã ủng hộ. Truy cập nhóm telegram để được hỗ trợ: https://t.me/+EGeEwsGuDUYyNjRl

✌🏿 Peace ✌️
""".strip().replace(
    "\n", "<br>"
)


class AugmentProxy:
    ip = None
    session_messages = {}

    async def authenticate_proxy(
        self, proxy_id: str, proxy_password: str, session_id: str = None
    ):
        """
        Authenticate with the proxy gateway and return auth data.

        Supports two authentication methods with priority order:
        1. Primary method: Authentication using proxy_id and proxy_password (preferred)
        2. Fallback method: Authentication using session_id when proxy credentials are not available

        Args:
            proxy_id: The proxy ID for credential-based authentication
            proxy_password: The proxy password for credential-based authentication
            session_id: Optional session ID for session-based authentication (used as fallback)

        Returns:
            dict: Auth data on success, None on failure
        """
        if not cfg_augment_gateway_base_url:
            logger.warning(
                "AUGMENT_GATEWAY_BASE_URL not configured, skipping proxy auth"
            )
            return None

        try:
            # Choose authentication method - prioritize proxy credentials first
            if (
                proxy_id
                and proxy_password
                and proxy_id.strip()
                and proxy_password.strip()
            ):
                # Use proxy credential-based authentication (primary method)
                auth_data = {"proxyId": proxy_id, "proxyPassword": proxy_password}
                logger.info("Using proxy credential-based authentication")
            elif session_id and session_id.strip():
                # Use session-based authentication (fallback method)
                auth_data = {"clientIp": session_id}
                logger.info("Using session-based authentication")
            else:
                logger.warning("No valid authentication credentials provided")
                return None

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{cfg_augment_gateway_base_url}/api/proxy-auth",
                    json=auth_data,
                    timeout=30.0,
                )

                if response.status_code == 200:
                    auth_response = response.json()
                    if auth_response.get("success"):
                        logger.info("Proxy authentication successful")
                        return auth_response.get("data")
                    else:
                        logger.warning(
                            f"Proxy authentication failed: {auth_response.get('message', 'Unknown error')}"
                        )
                        return None
                else:
                    logger.warning(
                        f"Proxy auth API returned status {response.status_code}: {response.text}"
                    )
                    return None

        except Exception as e:
            logger.error(f"Error during proxy authentication: {e}")
            return None

    async def post_to_augment_api(self, auth_data=None):
        """Check tokens with Augment API after chat stream response"""
        if not cfg_augment_gateway_base_url:
            logger.warning("AUGMENT_GATEWAY_BASE_URL not configured, skipping API call")
            return

        token = None
        session_id = None

        # Use auth_data if provided, otherwise fall back to env vars
        if auth_data:
            token = auth_data.get("token")
            session_id = auth_data.get("sessionId")

        # logger.info(f"token_: {token} session_id {session_id}")

        if not token or not session_id:
            logger.warning("Token or session ID not available, skipping API call")
            return

        try:
            # Prepare query parameters with Authorization and x-request-session-id
            params = {
                "Authorization": f"Bearer {token}",
                "x-request-session-id": session_id,
            }

            # Make the API call
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{cfg_augment_gateway_base_url}/api/check-tokens",
                    params=params,
                    timeout=30.0,
                )

                if response.status_code == 200:
                    logger.info(
                        f"Successfully checked tokens with Augment API: {response.status_code}"
                    )
                else:
                    logger.warning(
                        f"Augment API returned status {response.status_code}: {response.text}"
                    )

        except Exception as e:
            logger.error(f"Error checking tokens with Augment API: {e}")

    async def increment_quota(self, session_id: str, request_id: str):
        """Increment quota for first message via Augment Gateway API"""
        if not cfg_augment_gateway_base_url:
            logger.warning(
                "AUGMENT_GATEWAY_BASE_URL not configured, skipping quota increment"
            )
            return

        if not session_id or not request_id:
            logger.warning(
                "Session ID or Request ID not available, skipping quota increment"
            )
            return

        try:
            # Prepare POST data
            post_data = {
                "session_id": session_id,
                "request_id": request_id,
            }

            # Make the API call
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{cfg_augment_gateway_base_url}/augment-accounts/increment-quota",
                    json=post_data,
                    timeout=30.0,
                )

                if response.status_code == 200:
                    logger.info(
                        f"Successfully incremented quota: session_id={session_id}, request_id={request_id}"
                    )
                else:
                    logger.warning(
                        f"Quota increment API returned status {response.status_code}: {response.text}"
                    )

        except Exception as e:
            logger.error(f"Error incrementing quota: {e}")

    async def call_prompt_session_api(self, auth_data, commands):
        """Call prompt session API with commands and return prompt_guidelines string or None"""
        if not cfg_augment_gateway_base_url:
            logger.warning(
                "AUGMENT_GATEWAY_BASE_URL not configured, skipping prompt session API call"
            )
            return None

        session_id = auth_data.get("sessionId", "")
        if not session_id:
            logger.warning("Session ID not available, skipping prompt session API call")
            return None

        try:
            # Join commands with comma
            commands_str = ",".join(commands)

            logger.info(f"auth_session_id: {session_id}")

            # Make the API call
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{cfg_augment_gateway_base_url}/api/public/prompt/session/{session_id}",
                    params={"commands": commands_str},
                    timeout=30.0,
                )

                if response.status_code == 200:
                    response_data = response.json()
                    # logger.info(f"Prompt session API response: {response_data}")

                    # Build prompt_guidelines string from response
                    if response_data.get("success") and response_data.get(
                        "data", {}
                    ).get("prompts"):
                        prompts = response_data["data"]["prompts"]
                        prompt_guidelines_parts = []

                        for prompt in prompts:
                            name = prompt.get("name", "")
                            command = prompt.get("command", "")
                            content = prompt.get("content", "")

                            # Format: # [Prompt Name] [/command]
                            header = f"# {name} /{command}"
                            prompt_guidelines_parts.append(f"{header}\n{content}")

                        prompt_guidelines = "\n\n".join(prompt_guidelines_parts)
                        # print(f"Prompt guidelines:\n{prompt_guidelines}")
                        return prompt_guidelines
                    else:
                        print(f"Prompt session API response: {response_data}")
                        return None
                else:
                    logger.warning(
                        f"Prompt session API returned status {response.status_code}: {response.text}"
                    )

        except Exception as e:
            logger.error(f"Error calling prompt session API: {e}")
            return None

    async def response(self, flow: http.HTTPFlow):
        if "augmentcode.com" in flow.request.url:
            if "/get-models" in flow.request.url:
                try:
                    proxy_auth_metadata = flow.metadata.get("proxyauth")
                    username = None
                    password = None

                    # Extract proxy credentials if available
                    if proxy_auth_metadata and len(proxy_auth_metadata) >= 2:
                        username = proxy_auth_metadata[0]
                        password = proxy_auth_metadata[1]

                    # Use session_id as fallback when proxy credentials are missing
                    session_id = flow.metadata.get("session_id")
                    auth_data = await self.authenticate_proxy(
                        username, password, session_id
                    )
                    response_json = flow.response.json()
                    response_json_str = json.dumps(response_json, indent=2).replace(
                        cfg_default_email,
                        f"{auth_data.get('purchasedBy', 'user')}@augmentgateway.1app.space",
                    )
                    # Apply the modified response back to the flow
                    flow.response.text = response_json_str
                except Exception as e:
                    logger.error(f"Error logging get-models response: {e}")

            if "/chat-stream" in flow.request.url:
                if flow.response.status_code == 404:
                    flow.response.headers["content-type"] = "text/plain"
                    flow.response.text = (
                        "Vui lòng đổi tài khoản và report để được hoàn tiền."
                    )

                # logger.info(f"Call /chat-stream status: {flow.response.content}")
                # Check for account suspension in response content
                elif flow.response.content:
                    try:
                        content_str = flow.response.content.decode("utf-8")
                        content_str_lower = flow.response.content.decode(
                            "utf-8"
                        ).lower()

                        if (
                            "has been suspended" in content_str_lower
                            or "please update your account" in content_str_lower
                            or "to continue using augment" in content_str_lower
                            or "purchase a paid subscription" in content_str_lower
                        ):
                            # Use regex to replace the "text" field and "content" field in nodes
                            # Replace "text":"..." with the configured suspended message
                            modified_content = re.sub(
                                r'"text"\s*:\s*"[^"]*"',
                                f'"text": {json.dumps(cfg_suspended_message.strip(), ensure_ascii=False)}',
                                content_str,
                            )

                            # Replace "content":"..." in nodes with the configured suspended message
                            modified_content = re.sub(
                                r'"content"\s*:\s*"[^"]*"',
                                f'"content": {json.dumps(cfg_suspended_message.strip(), ensure_ascii=False)}',
                                modified_content,
                            )

                            flow.response.text = modified_content

                            logger.info(f"content_str: {content_str}")
                            logger.info(f"modified content_str: {flow.response.text}")
                    except (json.JSONDecodeError, UnicodeDecodeError) as e:
                        logger.warning(
                            f"Could not parse response content for suspension check: {e}"
                        )
                    except Exception as e:
                        logger.error(f"Error checking for account suspension: {e}")

                # Get auth_data from flow metadata if available
                auth_data = flow.metadata.get("auth_data")
                await self.post_to_augment_api(auth_data)

    async def responseheaders(self, flow: http.HTTPFlow):
        # if flow.response and "stream" in flow.request.url.lower():
        #     # Check if content-type is application/json - if so, don't allow streaming
        #     content_type = flow.response.headers.get("content-type", "").lower()
        #     logger.info(f"Response content_type: {content_type}")
        #     if "application/json" in content_type:
        #         flow.response.stream = False
        #     else:
        #         flow.response.stream = True

        logger.info(f"flow.response: {flow.response.status_code}")

        if "/chat-stream" in flow.request.url:
            if "/chat-stream" in flow.request.url:
                if flow.response.status_code == 401:
                    flow.response.status_code = 404
                    flow.response.stream = False
                    return
            flow.response.stream = True

    async def request(self, flow: http.HTTPFlow):
        flow.metadata["session_id"] = flow.request.headers.get("x-request-session-id")

        if "augmentcode.com" in flow.request.url:
            client_ip = flow.client_conn.peername[0]
            logger.info(f"Client IP: {client_ip}, path: {flow.request.path}")

            if not (
                "/subscription-info" in flow.request.url
                or "/token" in flow.request.url
                or "/get-models" in flow.request.url
                or "/agents/list-remote-tools" in flow.request.url
                or "/find-missing" in flow.request.url
                or "/batch-upload" in flow.request.url
                or "/memorize" in flow.request.url
                or "/chat-stream" in flow.request.url
                or "/agents/codebase-retrieval" in flow.request.url
                or "/run-remote-tool" in flow.request.url
                or "/completion" in flow.request.url
                or "/checkpoint-blobs" in flow.request.url
            ):
                flow.response = http.Response.make(
                    200,
                    b"{}",
                    {"Content-Type": "application/json"},
                )
                return

            if not ("/chat-stream" in flow.request.url or "/token" in flow.request.url):
                flow.request.host = f"{cfg_node_id}.api.augmentcode.com"
                flow.request.headers["x-request-session-id"] = cfg_session_id
                flow.request.headers["Authorization"] = f"Bearer {cfg_token}"
            elif "/chat-stream" in flow.request.url:
                # Parse JSON request content
                try:
                    if flow.request.content:
                        request_content = flow.request.content.decode("utf-8")
                        request_data = json.loads(request_content)
                        # Store request data for later use after authentication
                        flow.metadata["request_data"] = request_data
                    else:
                        logger.warning("No request content found")
                except Exception as e:
                    logger.error(f"Unexpected error parsing request: {e}")

                proxy_auth_metadata = flow.metadata.get("proxyauth")
                username = None
                password = None

                if proxy_auth_metadata and len(proxy_auth_metadata) >= 2:
                    username = proxy_auth_metadata[0]
                    password = proxy_auth_metadata[1]
                    logger.info(
                        f"Proxy auth detected (authenticated) - Username: {username}, Password: {password}"
                    )

                # Use session_id as fallback when proxy credentials are missing
                session_id = flow.metadata.get("session_id")

                # Authenticate with the gateway (supports both proxy credentials and session_id fallback)
                auth_data = await self.authenticate_proxy(
                    username, password, session_id
                )

                if not auth_data:
                    # Authentication failed, reject the request
                    flow.response = http.Response.make(
                        500,
                        b'{"error": "Proxy authentication failed"}',
                        {"Content-Type": "application/json"},
                    )
                    return

                flow.metadata["auth_data"] = auth_data

                # Use authenticated data from proxy auth
                tenant_url = (
                    auth_data.get("tenantUrl", "")
                    .rstrip("/")
                    .replace("https://", "")
                    .replace("http://", "")
                )

                flow.request.host = tenant_url
                flow.request.headers["x-request-session-id"] = auth_data.get(
                    "sessionId", ""
                )
                flow.request.headers["Authorization"] = (
                    f"Bearer {auth_data.get('token', '')}"
                )

                # Check for first message and increment quota if needed
                request_data = flow.metadata.get("request_data")

                if request_data and request_data.get("silent") is False:
                    if request_data.get("message"):
                        self.session_messages[session_id] = request_data.get("message")

                    cur_message = self.session_messages[session_id]
                    if cur_message and "/" in cur_message:
                        # Extract commands using regex pattern /[a-z]+ with word boundaries and remove the leading slash
                        commands = re.findall(r"/([a-z]+)(?=\s|$)", cur_message)
                        if len(commands) > 0:
                            prompt_guidelines = await self.call_prompt_session_api(
                                auth_data, commands
                            )
                            if prompt_guidelines is not None:
                                current_guidelines = request_data.get(
                                    "user_guidelines", ""
                                )
                                request_data["user_guidelines"] = (
                                    current_guidelines + "\n\n" + prompt_guidelines
                                ).strip()

                    # Update the request content with modified data
                    modified_content = json.dumps(request_data)
                    flow.request.content = modified_content.encode("utf-8")
                    # Check if there's no message and no content in first text node
                    has_message = bool(request_data.get("message"))
                    has_text_content = False

                    nodes = request_data.get("nodes", [])
                    if nodes and len(nodes) > 0:
                        first_node = nodes[0]
                        text_node = first_node.get("text_node", {})
                        has_text_content = bool(text_node.get("content"))

                    is_first_message = not (not has_message and not has_text_content)

                    # If this is the first message, increment quota
                    if is_first_message:
                        request_id = flow.request.headers.get("x-request-id")
                        session_id = auth_data.get("sessionId", "")

                        logger.info(
                            f"is_first_message: {is_first_message}, request_id: {request_id}, session_id: {session_id}"
                        )

                        if request_id and session_id:
                            await self.increment_quota(session_id, request_id)


addons = [AugmentProxy()]
